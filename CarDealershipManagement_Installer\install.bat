﻿@echo off
chcp 65001 >nul
title تثبيت برنامج إدارة معرض السيارات - Amr Ali Elawamy

echo.
echo ========================================
echo    📦 تثبيت برنامج إدارة معرض السيارات
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎯 مرحباً بك في برنامج إدارة معرض السيارات
echo.
echo الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل (64+ صلاحية)
echo    • نظام ضمان سلامة البيانات المالية
echo.

set "INSTALL_DIR=%PROGRAMFILES%\Car Dealership Management"

echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo.
echo هل تريد المتابعة مع التثبيت؟ (Y/N)
set /p "CONTINUE="
if /i not "%CONTINUE%"=="Y" (
    echo تم إلغاء التثبيت
    pause
    exit /b 0
)

echo.
echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ ملفات البرنامج...
xcopy "%~dp0*" "%INSTALL_DIR%\" /E /I /Y /EXCLUDE:%~dp0install.bat >nul

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم نسخ الملفات بنجاح
) else (
    echo ❌ فشل في نسخ الملفات
    pause
    exit /b 1
)

echo 🔗 إنشاء اختصارات...

REM اختصار سطح المكتب
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()"

REM اختصار قائمة البدء
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\برنامج إدارة معرض السيارات" mkdir "%START_MENU%\برنامج إدارة معرض السيارات"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\برنامج إدارة معرض السيارات\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()"

echo ✅ تم إنشاء الاختصارات

echo 🎉 تم تثبيت البرنامج بنجاح!
echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa (جميع الصلاحيات)
echo    المدير: admin / 123 (صلاحيات إدارية)
echo    المندوب: user / pass (صلاحيات أساسية)
echo.
echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج من سطح المكتب
echo    2. أو من قائمة البدء
echo    3. اختيار "نسخة تجريبية" للبدء فوراً
echo.
echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p "RUN_NOW="
if /i "%RUN_NOW%"=="Y" (
    start "" "%INSTALL_DIR%\CarDealershipManagement.exe"
)

echo.
echo 👨‍💻 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
pause
