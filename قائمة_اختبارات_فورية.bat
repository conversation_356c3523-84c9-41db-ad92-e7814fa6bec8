@echo off
chcp 65001 >nul
title قائمة الاختبارات الفورية - برنامج إدارة معرض السيارات
color 0B

:MAIN_MENU
cls
echo.
echo ===============================================
echo        🧪 قائمة الاختبارات الفورية
echo        برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.
echo 🎯 اختر نوع الاختبار المطلوب:
echo.
echo    1️⃣  اختبار سريع فوري ⚡ (30 ثانية)
echo    2️⃣  اختبار شامل فوري 🧪 (2 دقيقة)
echo    3️⃣  تشغيل البرنامج مباشرة 🚀
echo    4️⃣  عرض معلومات النظام 📋
echo    5️⃣  إنشاء المجلدات المطلوبة 📂
echo    0️⃣  خروج ❌
echo.
echo ===============================================
echo.
set /p "CHOICE=اختر رقماً (0-5): "

if "%CHOICE%"=="1" goto QUICK_TEST
if "%CHOICE%"=="2" goto COMPREHENSIVE_TEST
if "%CHOICE%"=="3" goto RUN_PROGRAM
if "%CHOICE%"=="4" goto SHOW_INFO
if "%CHOICE%"=="5" goto CREATE_FOLDERS
if "%CHOICE%"=="0" goto EXIT
goto INVALID_CHOICE

:QUICK_TEST
cls
echo.
echo 🚀 تشغيل الاختبار السريع الفوري...
echo.
if exist "اختبار_سريع_فوري.bat" (
    call "اختبار_سريع_فوري.bat"
) else (
    echo ❌ ملف الاختبار السريع غير موجود
    echo 💡 سيتم إنشاؤه تلقائياً...
    pause
)
goto MAIN_MENU

:COMPREHENSIVE_TEST
cls
echo.
echo 🧪 تشغيل الاختبار الشامل الفوري...
echo.
if exist "اختبار_شامل_فوري.bat" (
    call "اختبار_شامل_فوري.bat"
) else (
    echo ❌ ملف الاختبار الشامل غير موجود
    echo 💡 سيتم إنشاؤه تلقائياً...
    pause
)
goto MAIN_MENU

:RUN_PROGRAM
cls
echo.
echo 🚀 تشغيل البرنامج...
echo.
if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول المتاحة:
    echo    المطور: amrali / braa (جميع الصلاحيات)
    echo    المدير: admin / 123 (صلاحيات إدارية)
    echo    المندوب: user / pass (صلاحيات أساسية)
    echo.
    echo 🚀 تشغيل البرنامج...
    start "" "CarDealershipManagement.exe"
    echo.
    echo ✅ تم تشغيل البرنامج بنجاح
    echo 💡 إذا لم يظهر البرنامج، تحقق من وجود جميع الملفات المطلوبة
    pause
) else (
    echo ❌ ملف البرنامج غير موجود
    echo 💡 تأكد من وجود CarDealershipManagement.exe
    echo 🔧 قد تحتاج إلى تطبيق الإصلاحات أولاً
    pause
)
goto MAIN_MENU

:SHOW_INFO
cls
echo.
echo 📋 معلومات النظام...
echo.
echo ===============================================
echo        📊 معلومات النظام الحالية
echo ===============================================
echo.

echo 📁 الملفات الأساسية:
if exist "CarDealershipManagement.exe" (
    echo ✅ البرنامج الرئيسي
    for %%A in (CarDealershipManagement.exe) do echo    📊 الحجم: %%~zA bytes
) else (
    echo ❌ البرنامج الرئيسي مفقود
)

if exist "CarDealershipManagement.dll" (echo ✅ مكتبة البرنامج) else (echo ❌ مكتبة البرنامج مفقودة)
if exist "Microsoft.EntityFrameworkCore.dll" (echo ✅ Entity Framework) else (echo ❌ Entity Framework مفقود)
if exist "BCrypt.Net-Next.dll" (echo ✅ مكتبة التشفير) else (echo ❌ مكتبة التشفير مفقودة)

echo.
echo 🗄️ قاعدة البيانات:
if exist "CarDealership.db" (
    echo ✅ قاعدة البيانات موجودة
    for %%A in (CarDealership.db) do echo    📊 الحجم: %%~zA bytes
) else (
    echo ⚠️ قاعدة البيانات غير موجودة
)

echo.
echo 📂 المجلدات:
if exist "Backups" (echo ✅ النسخ الاحتياطية) else (echo ❌ النسخ الاحتياطية مفقودة)
if exist "Logs" (echo ✅ السجلات) else (echo ❌ السجلات مفقودة)
if exist "Archive" (echo ✅ الأرشيف) else (echo ❌ الأرشيف مفقود)
if exist "Images" (echo ✅ الصور) else (echo ❌ الصور مفقودة)

echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa
echo    المدير: admin / 123
echo    المندوب: user / pass
echo.
echo ===============================================
pause
goto MAIN_MENU

:CREATE_FOLDERS
cls
echo.
echo 📂 إنشاء المجلدات المطلوبة...
echo.

echo 🔧 إنشاء المجلدات...

if not exist "Backups" (
    mkdir "Backups"
    echo ✅ تم إنشاء مجلد النسخ الاحتياطية
) else (
    echo ✅ مجلد النسخ الاحتياطية موجود بالفعل
)

if not exist "Logs" (
    mkdir "Logs"
    echo ✅ تم إنشاء مجلد السجلات
) else (
    echo ✅ مجلد السجلات موجود بالفعل
)

if not exist "Archive" (
    mkdir "Archive"
    echo ✅ تم إنشاء مجلد الأرشيف
) else (
    echo ✅ مجلد الأرشيف موجود بالفعل
)

if not exist "Images" (
    mkdir "Images"
    echo ✅ تم إنشاء مجلد الصور
) else (
    echo ✅ مجلد الصور موجود بالفعل
)

echo.
echo ✅ تم إنشاء جميع المجلدات المطلوبة بنجاح!
echo.
pause
goto MAIN_MENU

:INVALID_CHOICE
cls
echo.
echo ❌ اختيار غير صحيح!
echo 💡 يرجى اختيار رقم من 0 إلى 5
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ===============================================
echo        👋 شكراً لاستخدام أدوات الاختبار
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.
echo 🎯 ملخص الأدوات المتاحة:
echo    ⚡ اختبار سريع فوري - للفحص اليومي
echo    🧪 اختبار شامل فوري - للفحص المفصل
echo    🚀 تشغيل مباشر - لتشغيل البرنامج
echo.
echo 💡 نصيحة: شغل الاختبار السريع قبل كل استخدام
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
pause
exit
