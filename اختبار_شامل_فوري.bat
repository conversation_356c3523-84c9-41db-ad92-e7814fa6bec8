@echo off
chcp 65001 >nul
title اختبار شامل فوري - برنامج إدارة معرض السيارات
color 0B

echo.
echo ===============================================
echo        🧪 اختبار شامل فوري للنظام
echo        برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 بدء الاختبار الشامل...
echo.

:: متغيرات الاختبار
set "TOTAL_TESTS=0"
set "PASSED_TESTS=0"
set "FAILED_TESTS=0"
set "WARNINGS=0"

:: 1. اختبار الملفات الأساسية
echo ===============================================
echo 📁 اختبار الملفات الأساسية
echo ===============================================

set /a TOTAL_TESTS+=6

echo 🔍 فحص الملفات الرئيسية...

if exist "CarDealershipManagement.exe" (
    echo ✅ CarDealershipManagement.exe
    set /a PASSED_TESTS+=1
    for %%A in (CarDealershipManagement.exe) do echo    📊 الحجم: %%~zA bytes
) else (
    echo ❌ CarDealershipManagement.exe - مفقود
    set /a FAILED_TESTS+=1
)

if exist "CarDealershipManagement.dll" (
    echo ✅ CarDealershipManagement.dll
    set /a PASSED_TESTS+=1
) else (
    echo ❌ CarDealershipManagement.dll - مفقود
    set /a FAILED_TESTS+=1
)

if exist "Microsoft.EntityFrameworkCore.dll" (
    echo ✅ Microsoft.EntityFrameworkCore.dll
    set /a PASSED_TESTS+=1
) else (
    echo ❌ Microsoft.EntityFrameworkCore.dll - مفقود
    set /a FAILED_TESTS+=1
)

if exist "BCrypt.Net-Next.dll" (
    echo ✅ BCrypt.Net-Next.dll
    set /a PASSED_TESTS+=1
) else (
    echo ❌ BCrypt.Net-Next.dll - مفقود
    set /a FAILED_TESTS+=1
)

if exist "Microsoft.Data.Sqlite.dll" (
    echo ✅ Microsoft.Data.Sqlite.dll
    set /a PASSED_TESTS+=1
) else (
    echo ❌ Microsoft.Data.Sqlite.dll - مفقود
    set /a FAILED_TESTS+=1
)

if exist "System.Management.dll" (
    echo ✅ System.Management.dll
    set /a PASSED_TESTS+=1
) else (
    echo ⚠️ System.Management.dll - مفقود
    set /a WARNINGS+=1
    set /a FAILED_TESTS+=1
)

echo.

:: 2. اختبار ملفات التكوين
echo ===============================================
echo ⚙️ اختبار ملفات التكوين
echo ===============================================

set /a TOTAL_TESTS+=2

if exist "CarDealershipManagement.runtimeconfig.json" (
    echo ✅ ملف التكوين موجود
    set /a PASSED_TESTS+=1
) else (
    echo ⚠️ ملف التكوين مفقود
    set /a WARNINGS+=1
    set /a FAILED_TESTS+=1
)

if exist "CarDealershipManagement.deps.json" (
    echo ✅ ملف التبعيات موجود
    set /a PASSED_TESTS+=1
) else (
    echo ⚠️ ملف التبعيات مفقود
    set /a WARNINGS+=1
    set /a FAILED_TESTS+=1
)

echo.

:: 3. اختبار قاعدة البيانات
echo ===============================================
echo 🗄️ اختبار قاعدة البيانات
echo ===============================================

set /a TOTAL_TESTS+=2

if exist "CarDealership.db" (
    echo ✅ قاعدة البيانات موجودة
    set /a PASSED_TESTS+=1
    
    for %%A in (CarDealership.db) do (
        echo    📊 الحجم: %%~zA bytes
        if %%~zA GTR 50000 (
            echo ✅ قاعدة البيانات تحتوي على بيانات كافية
            set /a PASSED_TESTS+=1
        ) else if %%~zA GTR 10000 (
            echo ⚠️ قاعدة البيانات تحتوي على بيانات قليلة
            set /a WARNINGS+=1
            set /a PASSED_TESTS+=1
        ) else (
            echo ⚠️ قاعدة البيانات صغيرة جداً
            set /a WARNINGS+=1
            set /a FAILED_TESTS+=1
        )
    )
) else (
    echo ⚠️ قاعدة البيانات غير موجودة
    echo 💡 سيتم إنشاؤها عند التشغيل الأول
    set /a WARNINGS+=1
    set /a FAILED_TESTS+=2
)

echo.

:: 4. اختبار المجلدات المطلوبة
echo ===============================================
echo 📂 اختبار وإنشاء المجلدات المطلوبة
echo ===============================================

set /a TOTAL_TESTS+=4

if exist "Backups" (
    echo ✅ مجلد النسخ الاحتياطية موجود
    set /a PASSED_TESTS+=1
) else (
    echo 🔧 إنشاء مجلد النسخ الاحتياطية...
    mkdir "Backups" 2>nul
    if exist "Backups" (
        echo ✅ تم إنشاء مجلد النسخ الاحتياطية
        set /a PASSED_TESTS+=1
    ) else (
        echo ❌ فشل في إنشاء مجلد النسخ الاحتياطية
        set /a FAILED_TESTS+=1
    )
)

if exist "Logs" (
    echo ✅ مجلد السجلات موجود
    set /a PASSED_TESTS+=1
) else (
    echo 🔧 إنشاء مجلد السجلات...
    mkdir "Logs" 2>nul
    if exist "Logs" (
        echo ✅ تم إنشاء مجلد السجلات
        set /a PASSED_TESTS+=1
    ) else (
        echo ❌ فشل في إنشاء مجلد السجلات
        set /a FAILED_TESTS+=1
    )
)

if exist "Archive" (
    echo ✅ مجلد الأرشيف موجود
    set /a PASSED_TESTS+=1
) else (
    echo 🔧 إنشاء مجلد الأرشيف...
    mkdir "Archive" 2>nul
    if exist "Archive" (
        echo ✅ تم إنشاء مجلد الأرشيف
        set /a PASSED_TESTS+=1
    ) else (
        echo ❌ فشل في إنشاء مجلد الأرشيف
        set /a FAILED_TESTS+=1
    )
)

if exist "Images" (
    echo ✅ مجلد الصور موجود
    set /a PASSED_TESTS+=1
) else (
    echo 🔧 إنشاء مجلد الصور...
    mkdir "Images" 2>nul
    if exist "Images" (
        echo ✅ تم إنشاء مجلد الصور
        set /a PASSED_TESTS+=1
    ) else (
        echo ❌ فشل في إنشاء مجلد الصور
        set /a FAILED_TESTS+=1
    )
)

echo.

:: 5. اختبار تشغيل البرنامج (اختياري)
echo ===============================================
echo 🚀 اختبار تشغيل البرنامج
echo ===============================================

set /a TOTAL_TESTS+=1

if exist "CarDealershipManagement.exe" (
    echo 💡 اختبار تشغيل البرنامج لمدة 3 ثوانٍ...
    start "" "CarDealershipManagement.exe"
    timeout /t 3 /nobreak >nul
    
    tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
    if %ERRORLEVEL%==0 (
        echo ✅ البرنامج يعمل بنجاح
        set /a PASSED_TESTS+=1
        echo 🔄 إغلاق البرنامج...
        taskkill /f /im "CarDealershipManagement.exe" >nul 2>&1
        timeout /t 1 /nobreak >nul
    ) else (
        echo ⚠️ البرنامج لم يبدأ أو توقف بسرعة
        set /a WARNINGS+=1
        set /a FAILED_TESTS+=1
    )
) else (
    echo ❌ لا يمكن اختبار التشغيل - ملف البرنامج مفقود
    set /a FAILED_TESTS+=1
)

echo.

:: 6. حساب النتائج
if %TOTAL_TESTS% GTR 0 (
    set /a SUCCESS_RATE=(%PASSED_TESTS% * 100) / %TOTAL_TESTS%
    set /a FAILURE_RATE=(%FAILED_TESTS% * 100) / %TOTAL_TESTS%
) else (
    set SUCCESS_RATE=0
    set FAILURE_RATE=100
)

:: 7. تقرير النتائج النهائي
echo ===============================================
echo        📊 تقرير النتائج النهائي
echo ===============================================
echo.
echo 📈 إحصائيات الاختبار:
echo    إجمالي الاختبارات: %TOTAL_TESTS%
echo    الاختبارات الناجحة: %PASSED_TESTS%
echo    الاختبارات الفاشلة: %FAILED_TESTS%
echo    التحذيرات: %WARNINGS%
echo    معدل النجاح: %SUCCESS_RATE%%%
echo    معدل الفشل: %FAILURE_RATE%%%
echo.

if %SUCCESS_RATE% GEQ 90 (
    echo 🎉 حالة النظام: ممتاز
    echo ✅ النظام جاهز للاستخدام الإنتاجي
    color 0A
) else if %SUCCESS_RATE% GEQ 70 (
    echo ⚠️ حالة النظام: جيد مع تحذيرات
    echo 💡 يمكن استخدام النظام مع مراقبة
    color 0E
) else if %SUCCESS_RATE% GEQ 50 (
    echo 🔧 حالة النظام: يحتاج إصلاح
    echo ❌ يُنصح بإصلاح المشاكل قبل الاستخدام
    color 0C
) else (
    echo ❌ حالة النظام: مشاكل خطيرة
    echo 🚨 يجب إصلاح المشاكل فوراً
    color 0C
)

echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa (جميع الصلاحيات)
echo    المدير: admin / 123 (صلاحيات إدارية)
echo    المندوب: user / pass (صلاحيات أساسية)
echo.

if %SUCCESS_RATE% GEQ 70 (
    echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
    set /p "RUN_PROGRAM="
    if /i "%RUN_PROGRAM%"=="Y" (
        if exist "CarDealershipManagement.exe" (
            echo 🚀 تشغيل البرنامج...
            start "" "CarDealershipManagement.exe"
            echo ✅ تم تشغيل البرنامج بنجاح
        ) else (
            echo ❌ ملف البرنامج غير موجود
        )
    )
) else (
    echo 💡 التوصيات:
    echo    1. شغل أداة الإصلاحات
    echo    2. تأكد من وجود جميع الملفات المطلوبة
    echo    3. أعد تشغيل الاختبار بعد الإصلاح
)

echo.
echo 👨‍💻 شكراً لاستخدام برنامج إدارة معرض السيارات!
echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
echo ===============================================
echo.
pause
