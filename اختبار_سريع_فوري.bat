@echo off
chcp 65001 >nul
title اختبار سريع فوري - برنامج إدارة معرض السيارات
color 0A

echo.
echo ===============================================
echo        ⚡ اختبار سريع فوري للنظام
echo        برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 فحص سريع للنظام...
echo.

:: اختبار الملفات الأساسية
echo 📁 الملفات الأساسية:
if exist "CarDealershipManagement.exe" (
    echo ✅ البرنامج الرئيسي موجود
    for %%A in (CarDealershipManagement.exe) do echo    📊 الحجم: %%~zA bytes
) else (
    echo ❌ البرنامج الرئيسي مفقود
)

if exist "CarDealershipManagement.dll" (
    echo ✅ مكتبة البرنامج موجودة
) else (
    echo ❌ مكتبة البرنامج مفقودة
)

if exist "Microsoft.EntityFrameworkCore.dll" (
    echo ✅ Entity Framework موجود
) else (
    echo ❌ Entity Framework مفقود
)

if exist "BCrypt.Net-Next.dll" (
    echo ✅ مكتبة التشفير موجودة
) else (
    echo ❌ مكتبة التشفير مفقودة
)

echo.

:: اختبار قاعدة البيانات
echo 🗄️ قاعدة البيانات:
if exist "CarDealership.db" (
    echo ✅ قاعدة البيانات موجودة
    for %%A in (CarDealership.db) do (
        if %%~zA GTR 10000 (
            echo ✅ قاعدة البيانات تحتوي على بيانات (%%~zA bytes)
        ) else (
            echo ⚠️ قاعدة البيانات صغيرة (%%~zA bytes)
        )
    )
) else (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند التشغيل
)

echo.

:: اختبار المجلدات
echo 📂 المجلدات المطلوبة:
if exist "Backups" (
    echo ✅ النسخ الاحتياطية
) else (
    mkdir "Backups" 2>nul
    echo 🔧 تم إنشاء مجلد النسخ الاحتياطية
)

if exist "Logs" (
    echo ✅ السجلات
) else (
    mkdir "Logs" 2>nul
    echo 🔧 تم إنشاء مجلد السجلات
)

if exist "Archive" (
    echo ✅ الأرشيف
) else (
    mkdir "Archive" 2>nul
    echo 🔧 تم إنشاء مجلد الأرشيف
)

if exist "Images" (
    echo ✅ الصور
) else (
    mkdir "Images" 2>nul
    echo 🔧 تم إنشاء مجلد الصور
)

echo.

:: تقييم سريع
set "STATUS=OK"
set "ISSUES=0"

if not exist "CarDealershipManagement.exe" (
    set "STATUS=ERROR"
    set /a ISSUES+=1
)
if not exist "CarDealershipManagement.dll" (
    set "STATUS=ERROR"
    set /a ISSUES+=1
)
if not exist "Microsoft.EntityFrameworkCore.dll" (
    set "STATUS=WARNING"
    set /a ISSUES+=1
)

echo ===============================================
echo        📋 النتيجة السريعة
echo ===============================================
echo.

if "%STATUS%"=="OK" (
    echo 🎉 النظام جاهز للاستخدام!
    echo ✅ جميع الملفات الأساسية موجودة
    echo ✅ المجلدات المطلوبة متوفرة
    color 0A
) else if "%STATUS%"=="WARNING" (
    echo ⚠️ النظام يعمل مع تحذيرات
    echo 💡 بعض الملفات مفقودة لكن يمكن تشغيل البرنامج
    color 0E
) else (
    echo ❌ النظام يحتاج إصلاح!
    echo 🔧 ملفات أساسية مفقودة
    echo 💡 شغل أداة الإصلاحات أولاً
    color 0C
)

echo.
echo 📊 عدد المشاكل المكتشفة: %ISSUES%
echo.

if "%STATUS%"=="OK" (
    echo 🔑 بيانات الدخول:
    echo    المطور: amrali / braa (جميع الصلاحيات)
    echo    المدير: admin / 123 (صلاحيات إدارية)
    echo    المندوب: user / pass (صلاحيات أساسية)
    echo.
    
    echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
    set /p "RUN="
    if /i "%RUN%"=="Y" (
        echo 🚀 تشغيل البرنامج...
        start "" "CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج بنجاح
    )
) else (
    echo 💡 التوصيات:
    echo    1. شغل أداة الإصلاحات
    echo    2. تأكد من وجود جميع الملفات
    echo    3. أعد تشغيل الاختبار
)

echo.
echo 👨‍💻 المطور: Amr Ali Elawamy - 01285626623
echo 📧 البريد: <EMAIL>
echo.
echo ===============================================
echo.
pause
