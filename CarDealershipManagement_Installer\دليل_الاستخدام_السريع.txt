🚀 دليل الاستخدام السريع
========================

برنامج إدارة معرض السيارات
المطور: Amr Ali Elawamy
الهاتف: 01285626623
البريد: <EMAIL>

🎯 البدء السريع:
================

1. لتشغيل البرنامج مباشرة:
   انقر نقراً مزدوجاً على: تشغيل_البرنامج.bat

2. لاختبار البرنامج أولاً:
   انقر نقراً مزدوجاً على: اختبار_البرنامج_النهائي.bat

3. لتشغيل البرنامج مباشرة:
   انقر نقراً مزدوجاً على: CarDealershipManagement.exe

🔑 بيانات الدخول:
==================

👑 المطور (جميع الصلاحيات):
   اسم المستخدم: amrali
   كلمة المرور: braa

👨‍💼 المدير (صلاحيات إدارية):
   اسم المستخدم: admin
   كلمة المرور: 123

👤 المندوب (صلاحيات أساسية):
   اسم المستخدم: user
   كلمة المرور: pass

📋 الميزات الرئيسية:
====================

🚗 إدارة المخزون:
   • إضافة وتعديل السيارات
   • تتبع المخزون والكميات
   • إدارة الصور والمواصفات

👥 إدارة العملاء:
   • قاعدة بيانات العملاء
   • تتبع المعاملات
   • كشوف الحسابات

💰 نظام المبيعات:
   • تسجيل المبيعات
   • نظام الأقساط
   • إدارة المدفوعات

📊 التقارير:
   • تقارير المبيعات
   • تقارير المخزون
   • التقارير المالية

🔧 الإدارة:
   • إدارة المستخدمين
   • النسخ الاحتياطية
   • إعدادات النظام

📁 هيكل المجلدات:
==================

📂 Backups - النسخ الاحتياطية
📂 Logs - ملفات السجلات
📂 Archive - الأرشيف
   📁 التقارير
   📁 السيارات
   📁 الصور
   📁 العملاء
   📁 المبيعات
   📁 المستندات الرسمية
   📁 الموردين
   📁 النسخ الاحتياطية
📂 Images - صور السيارات
📂 Resources - الموارد والأيقونات

🗄️ قاعدة البيانات:
===================

📄 CarDealership.db - قاعدة البيانات الرئيسية
   • جداول السيارات والعملاء
   • بيانات المبيعات والأقساط
   • معلومات المستخدمين والصلاحيات
   • إعدادات النظام

🔧 أدوات الصيانة:
==================

🧪 اختبار_البرنامج_النهائي.bat
   • فحص شامل للنظام
   • اختبار جميع المكونات
   • تقرير مفصل بالحالة

🚀 تشغيل_البرنامج.bat
   • تشغيل آمن للبرنامج
   • عرض بيانات الدخول
   • فحص سريع للملفات

🔄 factory_reset.bat
   • إعادة تعيين النظام
   • حذف البيانات والعودة للإعدادات الافتراضية

🔍 system_check.bat
   • فحص سريع للنظام
   • التحقق من الملفات الأساسية

💡 نصائح مهمة:
==============

✅ قبل الاستخدام:
   • شغل اختبار البرنامج أولاً
   • تأكد من وجود جميع الملفات
   • أنشئ نسخة احتياطية

✅ أثناء الاستخدام:
   • استخدم حساب المطور للوصول الكامل
   • احفظ عملك بانتظام
   • أنشئ نسخ احتياطية دورية

✅ عند المشاكل:
   • شغل البرنامج كمدير
   • تحقق من ملفات السجلات
   • اتصل بالدعم الفني

🚨 تحذيرات مهمة:
=================

⚠️ لا تحذف ملف قاعدة البيانات
⚠️ لا تعدل الملفات يدوياً
⚠️ أنشئ نسخة احتياطية قبل أي تحديث
⚠️ استخدم كلمات مرور قوية

📞 الدعم الفني:
===============

في حالة الحاجة للمساعدة:
📞 الهاتف: 01285626623
📧 البريد: <EMAIL>
🕒 متوفر: 24/7

أنواع الدعم المتاح:
• مساعدة في الاستخدام
• حل المشاكل التقنية
• تدريب على البرنامج
• تخصيص الميزات

🎯 خطوات الاستخدام المثلى:
============================

1. شغل اختبار البرنامج النهائي
2. إذا كانت النتيجة ممتازة، شغل البرنامج
3. سجل الدخول بحساب المطور
4. استكشف الميزات المختلفة
5. أنشئ نسخة احتياطية أولى
6. ابدأ إدخال البيانات

🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
