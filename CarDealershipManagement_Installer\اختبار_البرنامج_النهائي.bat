@echo off
chcp 65001 >nul
title اختبار البرنامج النهائي - برنامج إدارة معرض السيارات
color 0A

echo.
echo ===============================================
echo        🎉 اختبار البرنامج النهائي
echo        برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 فحص شامل للبرنامج النهائي...
echo.

:: متغيرات الاختبار
set "TOTAL_CHECKS=0"
set "PASSED_CHECKS=0"
set "FAILED_CHECKS=0"

:: 1. اختبار الملفات الأساسية
echo ===============================================
echo 📁 اختبار الملفات الأساسية
echo ===============================================

set /a TOTAL_CHECKS+=5

echo 🔍 فحص الملفات الرئيسية...

if exist "CarDealershipManagement.exe" (
    echo ✅ CarDealershipManagement.exe
    set /a PASSED_CHECKS+=1
    for %%A in (CarDealershipManagement.exe) do echo    📊 الحجم: %%~zA bytes
) else (
    echo ❌ CarDealershipManagement.exe - مفقود
    set /a FAILED_CHECKS+=1
)

if exist "CarDealershipManagement.dll" (
    echo ✅ CarDealershipManagement.dll
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ CarDealershipManagement.dll - مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Microsoft.EntityFrameworkCore.dll" (
    echo ✅ Microsoft.EntityFrameworkCore.dll
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ Microsoft.EntityFrameworkCore.dll - مفقود
    set /a FAILED_CHECKS+=1
)

if exist "BCrypt.Net-Next.dll" (
    echo ✅ BCrypt.Net-Next.dll
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ BCrypt.Net-Next.dll - مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Microsoft.Data.Sqlite.dll" (
    echo ✅ Microsoft.Data.Sqlite.dll
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ Microsoft.Data.Sqlite.dll - مفقود
    set /a FAILED_CHECKS+=1
)

echo.

:: 2. اختبار قاعدة البيانات
echo ===============================================
echo 🗄️ اختبار قاعدة البيانات
echo ===============================================

set /a TOTAL_CHECKS+=2

if exist "CarDealership.db" (
    echo ✅ قاعدة البيانات موجودة
    set /a PASSED_CHECKS+=1
    
    for %%A in (CarDealership.db) do (
        echo    📊 الحجم: %%~zA bytes
        if %%~zA GTR 100000 (
            echo ✅ قاعدة البيانات تحتوي على بيانات كاملة
            set /a PASSED_CHECKS+=1
        ) else if %%~zA GTR 50000 (
            echo ⚠️ قاعدة البيانات تحتوي على بيانات جيدة
            set /a PASSED_CHECKS+=1
        ) else (
            echo ⚠️ قاعدة البيانات تحتوي على بيانات أساسية
            set /a PASSED_CHECKS+=1
        )
    )
) else (
    echo ❌ قاعدة البيانات غير موجودة
    set /a FAILED_CHECKS+=2
)

echo.

:: 3. اختبار المجلدات والموارد
echo ===============================================
echo 📂 اختبار المجلدات والموارد
echo ===============================================

set /a TOTAL_CHECKS+=6

if exist "Backups" (
    echo ✅ مجلد النسخ الاحتياطية
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ مجلد النسخ الاحتياطية مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Logs" (
    echo ✅ مجلد السجلات
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ مجلد السجلات مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Archive" (
    echo ✅ مجلد الأرشيف
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ مجلد الأرشيف مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Images" (
    echo ✅ مجلد الصور
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ مجلد الصور مفقود
    set /a FAILED_CHECKS+=1
)

if exist "Resources" (
    echo ✅ مجلد الموارد
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ مجلد الموارد مفقود
    set /a FAILED_CHECKS+=1
)

if exist "app-icon.ico" (
    echo ✅ أيقونة البرنامج
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ أيقونة البرنامج مفقودة
    set /a FAILED_CHECKS+=1
)

echo.

:: 4. اختبار ملفات التكوين
echo ===============================================
echo ⚙️ اختبار ملفات التكوين
echo ===============================================

set /a TOTAL_CHECKS+=3

if exist "CarDealershipManagement.runtimeconfig.json" (
    echo ✅ ملف التكوين
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ ملف التكوين مفقود
    set /a FAILED_CHECKS+=1
)

if exist "CarDealershipManagement.deps.json" (
    echo ✅ ملف التبعيات
    set /a PASSED_CHECKS+=1
) else (
    echo ❌ ملف التبعيات مفقود
    set /a FAILED_CHECKS+=1
)

if exist "installation.auth" (
    echo ✅ ملف التفويض
    set /a PASSED_CHECKS+=1
) else (
    echo ⚠️ ملف التفويض مفقود
    set /a FAILED_CHECKS+=1
)

echo.

:: 5. اختبار تشغيل البرنامج
echo ===============================================
echo 🚀 اختبار تشغيل البرنامج
echo ===============================================

set /a TOTAL_CHECKS+=1

echo 💡 اختبار تشغيل البرنامج لمدة 5 ثوانٍ...
start "" "CarDealershipManagement.exe"
timeout /t 5 /nobreak >nul

tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
if %ERRORLEVEL%==0 (
    echo ✅ البرنامج يعمل بنجاح
    set /a PASSED_CHECKS+=1
    echo 🔄 إغلاق البرنامج...
    taskkill /f /im "CarDealershipManagement.exe" >nul 2>&1
    timeout /t 2 /nobreak >nul
) else (
    echo ⚠️ البرنامج لم يبدأ أو توقف بسرعة
    set /a FAILED_CHECKS+=1
)

echo.

:: 6. حساب النتائج النهائية
if %TOTAL_CHECKS% GTR 0 (
    set /a SUCCESS_RATE=(%PASSED_CHECKS% * 100) / %TOTAL_CHECKS%
) else (
    set SUCCESS_RATE=0
)

:: 7. تقرير النتائج النهائي
echo ===============================================
echo        🏆 تقرير النتائج النهائي
echo ===============================================
echo.
echo 📊 إحصائيات الاختبار:
echo    إجمالي الفحوصات: %TOTAL_CHECKS%
echo    الفحوصات الناجحة: %PASSED_CHECKS%
echo    الفحوصات الفاشلة: %FAILED_CHECKS%
echo    معدل النجاح: %SUCCESS_RATE%%%
echo.

if %SUCCESS_RATE% GEQ 95 (
    echo 🎉 حالة البرنامج: ممتاز - جاهز للإنتاج
    echo ✅ البرنامج في أفضل حالاته
    color 0A
) else if %SUCCESS_RATE% GEQ 85 (
    echo ⭐ حالة البرنامج: جيد جداً - جاهز للاستخدام
    echo ✅ البرنامج يعمل بكفاءة عالية
    color 0A
) else if %SUCCESS_RATE% GEQ 70 (
    echo ⚠️ حالة البرنامج: جيد مع تحذيرات
    echo 💡 يمكن الاستخدام مع مراقبة
    color 0E
) else (
    echo ❌ حالة البرنامج: يحتاج إصلاح
    echo 🔧 يُنصح بإصلاح المشاكل
    color 0C
)

echo.
echo 🔑 بيانات الدخول المؤكدة:
echo    المطور: amrali / braa (جميع الصلاحيات)
echo    المدير: admin / 123 (صلاحيات إدارية)
echo    المندوب: user / pass (صلاحيات أساسية)
echo.

echo 📋 الميزات المتاحة:
echo    ✅ إدارة المخزون والسيارات
echo    ✅ إدارة العملاء والموردين
echo    ✅ نظام المبيعات والأقساط
echo    ✅ التقارير والإحصائيات
echo    ✅ النسخ الاحتياطية والأرشفة
echo    ✅ إدارة المستخدمين والصلاحيات
echo.

if %SUCCESS_RATE% GEQ 85 (
    echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
    set /p "RUN_PROGRAM="
    if /i "%RUN_PROGRAM%"=="Y" (
        echo 🚀 تشغيل البرنامج...
        start "" "CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج بنجاح
        echo 💡 استخدم بيانات الدخول المذكورة أعلاه
    )
) else (
    echo 💡 التوصيات:
    echo    1. تحقق من وجود جميع الملفات المطلوبة
    echo    2. شغل أداة الإصلاحات إذا لزم الأمر
    echo    3. أعد تشغيل الاختبار بعد الإصلاح
)

echo.
echo 👨‍💻 شكراً لاستخدام برنامج إدارة معرض السيارات!
echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo ===============================================
echo.
pause
