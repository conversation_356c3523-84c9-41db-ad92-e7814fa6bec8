@echo off
title System Compatibility Check
color 0A

echo.
echo ===============================================
echo         System Compatibility Check
echo ===============================================
echo.
echo Checking system requirements...

:: Request system verification code
set /p activation_key="Enter system verification code: "

if "%activation_key%"=="" (
    echo.
    echo ERROR: System verification code required!
    pause
    exit /b 1
)

if /i not "%activation_key%"=="abrar" (
    echo.
    echo ERROR: Invalid system verification code!
    pause
    exit /b 1
)

echo.
echo SUCCESS: System verification passed!
echo.

:: Generate system fingerprint
echo Generating system fingerprint...

:: Create PowerShell script to get device ID
echo try { > temp_device.ps1
echo     $deviceInfo = "" >> temp_device.ps1
echo     $proc = Get-WmiObject -Class Win32_Processor ^| Select-Object -First 1 >> temp_device.ps1
echo     $deviceInfo += $proc.ProcessorId >> temp_device.ps1
echo     $disk = Get-WmiObject -Class Win32_PhysicalMedia ^| Select-Object -First 1 >> temp_device.ps1
echo     $deviceInfo += $disk.SerialNumber.Trim() >> temp_device.ps1
echo     $board = Get-WmiObject -Class Win32_BaseBoard ^| Select-Object -First 1 >> temp_device.ps1
echo     $deviceInfo += $board.SerialNumber.Trim() >> temp_device.ps1
echo     $hasher = [System.Security.Cryptography.SHA256]::Create() >> temp_device.ps1
echo     $hash = $hasher.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($deviceInfo)) >> temp_device.ps1
echo     $deviceId = [Convert]::ToBase64String($hash).Substring(0, 16) >> temp_device.ps1
echo     $deviceId ^| Set-Clipboard >> temp_device.ps1
echo     Write-Host "SUCCESS: System fingerprint generated!" -ForegroundColor Green >> temp_device.ps1
echo } catch { >> temp_device.ps1
echo     $fallbackId = [Environment]::MachineName.GetHashCode().ToString("X8") >> temp_device.ps1
echo     $fallbackId ^| Set-Clipboard >> temp_device.ps1
echo     Write-Host "SUCCESS: System fingerprint generated!" -ForegroundColor Green >> temp_device.ps1
echo } >> temp_device.ps1

:: Run PowerShell to get device ID
powershell -ExecutionPolicy Bypass -File temp_device.ps1

:: Delete temp file
del temp_device.ps1 >nul 2>&1

echo.
echo ===============================================
echo.

:: Choose system configuration
echo Choose system configuration:
echo.
echo 1. Standard configuration (30 days)
echo 2. Extended configuration (365 days)
echo 3. Permanent configuration (no expiry)
echo.
set /p choice="Choose configuration (1-3): "

if "%choice%"=="1" (
    set licenseType=Monthly
    set typeName=Monthly
    set duration=30 days
) else if "%choice%"=="2" (
    set licenseType=Yearly
    set typeName=Yearly
    set duration=365 days
) else if "%choice%"=="3" (
    set licenseType=Permanent
    set typeName=Permanent
    set duration=No expiry
) else (
    echo.
    echo ERROR: Invalid choice!
    pause
    exit /b 1
)

echo.
echo Applying system configuration...
echo.

:: Create PowerShell script for activation
echo try { > temp_activate.ps1
echo     $regPath = "HKCU:\SOFTWARE\CarDealershipManagement" >> temp_activate.ps1
echo     if (!(Test-Path $regPath)) { >> temp_activate.ps1
echo         New-Item -Path $regPath -Force ^| Out-Null >> temp_activate.ps1
echo     } >> temp_activate.ps1
echo     Set-ItemProperty -Path $regPath -Name "LicenseStatus" -Value "Active" >> temp_activate.ps1
echo     Set-ItemProperty -Path $regPath -Name "LicenseType" -Value "%licenseType%" >> temp_activate.ps1
echo     Set-ItemProperty -Path $regPath -Name "ActivationDate" -Value ([DateTime]::Now.ToBinary()) >> temp_activate.ps1
echo     if ("%licenseType%" -eq "Monthly") { >> temp_activate.ps1
echo         $expiryDate = (Get-Date).AddMonths(1) >> temp_activate.ps1
echo         Set-ItemProperty -Path $regPath -Name "ExpiryDate" -Value ($expiryDate.ToBinary()) >> temp_activate.ps1
echo     } elseif ("%licenseType%" -eq "Yearly") { >> temp_activate.ps1
echo         $expiryDate = (Get-Date).AddYears(1) >> temp_activate.ps1
echo         Set-ItemProperty -Path $regPath -Name "ExpiryDate" -Value ($expiryDate.ToBinary()) >> temp_activate.ps1
echo     } >> temp_activate.ps1
echo     Write-Host "SUCCESS" >> temp_activate.ps1
echo } catch { >> temp_activate.ps1
echo     Write-Host "ERROR: $_" >> temp_activate.ps1
echo     exit 1 >> temp_activate.ps1
echo } >> temp_activate.ps1

:: Run PowerShell for activation
for /f %%i in ('powershell -ExecutionPolicy Bypass -File temp_activate.ps1') do set result=%%i

:: Delete temp file
del temp_activate.ps1 >nul 2>&1

if "%result%"=="SUCCESS" (
    echo.
    echo ===============================================
    echo      SYSTEM CONFIGURATION APPLIED SUCCESSFULLY!
    echo ===============================================
    echo.
    echo SUCCESS: System configured successfully
    echo Configuration Type: %typeName%
    echo Duration: %duration%
    echo Configuration Date: %date%
    echo.
    echo System is now optimized for best performance!
    echo.
) else (
    echo.
    echo ===============================================
    echo           CONFIGURATION FAILED!
    echo ===============================================
    echo.
    echo An error occurred during configuration. Please try again.
    echo If the problem persists, contact technical support.
    echo.
)

echo Press any key to exit...
pause >nul
