@echo off
chcp 65001 >nul
title تشغيل برنامج إدارة معرض السيارات
color 0B

echo.
echo ===============================================
echo        🚀 تشغيل برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 التحقق من جاهزية البرنامج...
echo.

:: فحص سريع للملفات الأساسية
if exist "CarDealershipManagement.exe" (
    echo ✅ ملف البرنامج الرئيسي موجود
) else (
    echo ❌ ملف البرنامج الرئيسي مفقود
    echo 💡 تأكد من وجود CarDealershipManagement.exe
    pause
    exit
)

if exist "CarDealership.db" (
    echo ✅ قاعدة البيانات موجودة
    for %%A in (CarDealership.db) do echo    📊 حجم قاعدة البيانات: %%~zA bytes
) else (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند التشغيل
)

echo.
echo ===============================================
echo        🔑 بيانات الدخول المتاحة
echo ===============================================
echo.
echo 👑 المطور (جميع الصلاحيات):
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.
echo 👨‍💼 المدير (صلاحيات إدارية):
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo.
echo 👤 المندوب (صلاحيات أساسية):
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo.
echo ===============================================
echo.

echo 💡 نصائح مهمة:
echo    • استخدم حساب المطور للوصول الكامل
echo    • احتفظ بنسخة احتياطية من قاعدة البيانات
echo    • تأكد من إغلاق البرنامج بشكل صحيح
echo.

echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p "START_PROGRAM="

if /i "%START_PROGRAM%"=="Y" (
    echo.
    echo 🚀 تشغيل البرنامج...
    echo.
    
    :: التحقق من تشغيل البرنامج مسبقاً
    tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
    if %ERRORLEVEL%==0 (
        echo ⚠️ البرنامج يعمل بالفعل
        echo 💡 تحقق من شريط المهام أو أغلق البرنامج أولاً
        pause
        exit
    )
    
    :: تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    :: انتظار قصير للتحقق من التشغيل
    timeout /t 3 /nobreak >nul
    
    :: التحقق من نجاح التشغيل
    tasklist /fi "imagename eq CarDealershipManagement.exe" 2>nul | find /i "CarDealershipManagement.exe" >nul
    if %ERRORLEVEL%==0 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        echo.
        echo 🎉 البرنامج جاهز للاستخدام
        echo 💡 استخدم بيانات الدخول المذكورة أعلاه
        echo.
        echo 📋 الميزات المتاحة:
        echo    • إدارة المخزون والسيارات
        echo    • إدارة العملاء والموردين  
        echo    • نظام المبيعات والأقساط
        echo    • التقارير والإحصائيات
        echo    • النسخ الاحتياطية
        echo.
    ) else (
        echo ❌ فشل في تشغيل البرنامج
        echo.
        echo 💡 الأسباب المحتملة:
        echo    • ملفات مفقودة أو تالفة
        echo    • مشكلة في قاعدة البيانات
        echo    • نقص في الصلاحيات
        echo.
        echo 🔧 الحلول المقترحة:
        echo    • شغل البرنامج كمدير (Run as Administrator)
        echo    • تحقق من وجود جميع الملفات المطلوبة
        echo    • شغل أداة الاختبار للتشخيص
        echo.
    )
) else (
    echo.
    echo 👋 شكراً لك!
    echo 💡 يمكنك تشغيل البرنامج في أي وقت
)

echo.
echo ===============================================
echo.
echo 📞 للدعم الفني:
echo    الهاتف: 01285626623
echo    البريد: <EMAIL>
echo    متوفر 24/7
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 🏆 جميع الحقوق محفوظة © 2024
echo.
echo ===============================================
echo.
pause
